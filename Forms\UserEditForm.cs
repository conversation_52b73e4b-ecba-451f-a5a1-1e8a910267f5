using SqlSugarDemo.Models;

namespace SqlSugarDemo.Forms
{
    public partial class UserEditForm : Form
    {
        public User User { get; private set; }
        private readonly bool _isEditMode;

        public UserEditForm() : this(null)
        {
        }

        public UserEditForm(User? user)
        {
            InitializeComponent();
            _isEditMode = user != null;
            
            if (_isEditMode)
            {
                User = user!;
                LoadUserData();
                Text = "编辑用户";
            }
            else
            {
                User = new User();
                Text = "添加用户";
            }
        }

        private void LoadUserData()
        {
            txtName.Text = User.Name;
            txtEmail.Text = User.Email;
            numAge.Value = User.Age ?? 0;
            chkIsActive.Checked = User.IsActive;
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(txtName.Text))
            {
                MessageBox.Show("请输入用户名！", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtName.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtEmail.Text))
            {
                MessageBox.Show("请输入邮箱！", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtEmail.Focus();
                return false;
            }

            // 简单的邮箱格式验证
            if (!txtEmail.Text.Contains("@") || !txtEmail.Text.Contains("."))
            {
                MessageBox.Show("请输入有效的邮箱格式！", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtEmail.Focus();
                return false;
            }

            return true;
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            if (!ValidateInput())
                return;

            User.Name = txtName.Text.Trim();
            User.Email = txtEmail.Text.Trim();
            User.Age = numAge.Value == 0 ? null : (int)numAge.Value;
            User.IsActive = chkIsActive.Checked;

            if (!_isEditMode)
            {
                User.CreateTime = DateTime.Now;
            }

            DialogResult = DialogResult.OK;
            Close();
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }
    }
}
