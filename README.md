# SqlSugar Demo - WinForms 用户管理系统

这是一个使用 C# 和 .NET 8.0 开发的 WinForms 应用程序，演示了 SqlSugar V5.1.4.198 ORM 框架的基本使用方法。

## 项目特性

- ✅ .NET 8.0 + WinForms
- ✅ SqlSugar V5.1.4.198 ORM 框架
- ✅ SQLite 数据库
- ✅ 现代化架构设计（Repository 模式）
- ✅ 完整的 CRUD 操作
- ✅ 异步编程支持
- ✅ 简洁的用户界面

## 项目结构

```
SqlSugarDemo/
├── Models/                 # 数据模型
│   └── User.cs            # 用户实体类
├── Repositories/          # 数据访问层
│   ├── IUserRepository.cs # 用户仓储接口
│   └── UserRepository.cs  # 用户仓储实现
├── Services/              # 服务层
│   └── DatabaseService.cs # 数据库服务
├── Forms/                 # 窗体
│   ├── MainForm.cs        # 主窗体
│   ├── MainForm.Designer.cs
│   ├── UserEditForm.cs    # 用户编辑窗体
│   └── UserEditForm.Designer.cs
├── Program.cs             # 程序入口
└── SqlSugarDemo.csproj    # 项目文件
```

## 功能特性

### 1. 用户管理
- 查看所有用户列表
- 添加新用户
- 编辑现有用户
- 删除用户
- 按姓名搜索用户

### 2. SqlSugar 功能演示
- **实体映射**: 使用 `[SugarTable]` 和 `[SugarColumn]` 特性
- **数据库初始化**: 自动创建表结构和初始数据
- **CRUD 操作**: 增删改查的完整实现
- **异步操作**: 所有数据库操作都使用异步方法
- **SQL 日志**: 开发环境下打印执行的 SQL 语句
- **条件查询**: 演示 Where 条件和模糊搜索

## 运行要求

- .NET 8.0 SDK
- Windows 操作系统
- Visual Studio 2022 或 VS Code

## 如何运行

1. 克隆或下载项目到本地
2. 打开命令行，导航到项目目录
3. 运行以下命令：

```bash
# 还原 NuGet 包
dotnet restore

# 编译项目
dotnet build

# 运行程序
dotnet run
```

或者在 Visual Studio 中直接打开 `SqlSugarDemo.csproj` 文件并运行。

## 数据库

项目使用 SQLite 数据库，数据库文件 `demo.db` 会在程序首次运行时自动创建在项目根目录下。

### 用户表结构

| 字段名 | 类型 | 说明 |
|--------|------|------|
| Id | INTEGER | 主键，自增 |
| Name | TEXT(50) | 用户名，非空 |
| Email | TEXT(100) | 邮箱，非空 |
| Age | INTEGER | 年龄，可空 |
| CreateTime | DATETIME | 创建时间，非空 |
| IsActive | BOOLEAN | 是否激活，非空 |

## SqlSugar 核心代码示例

### 1. 实体定义
```csharp
[SugarTable("Users")]
public class User
{
    [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
    public int Id { get; set; }
    
    [SugarColumn(Length = 50, IsNullable = false)]
    public string Name { get; set; } = string.Empty;
    
    // ... 其他属性
}
```

### 2. 数据库配置
```csharp
var db = new SqlSugarScope(new ConnectionConfig()
{
    ConnectionString = "Data Source=demo.db;Version=3;",
    DbType = DbType.Sqlite,
    IsAutoCloseConnection = true,
    InitKeyType = InitKeyType.Attribute
});
```

### 3. CRUD 操作
```csharp
// 查询所有
var users = await db.Queryable<User>().ToListAsync();

// 添加
await db.Insertable(user).ExecuteCommandAsync();

// 更新
await db.Updateable(user).ExecuteCommandAsync();

// 删除
await db.Deleteable<User>().Where(u => u.Id == id).ExecuteCommandAsync();
```

## 学习要点

1. **ORM 映射**: 了解如何使用特性进行实体映射
2. **Repository 模式**: 数据访问层的抽象和实现
3. **异步编程**: 数据库操作的异步处理
4. **依赖注入**: 简单的依赖管理
5. **错误处理**: 数据库操作的异常处理

这个示例项目展示了 SqlSugar 在实际项目中的基本使用方法，适合初学者学习和参考。
