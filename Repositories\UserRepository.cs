using SqlSugar;
using SqlSugarDemo.Models;
using SqlSugarDemo.Services;

namespace SqlSugarDemo.Repositories
{
    /// <summary>
    /// 用户仓储实现类
    /// </summary>
    public class UserRepository : IUserRepository
    {
        /// <summary>
        /// 获取所有用户
        /// </summary>
        /// <returns></returns>
        public async Task<List<User>> GetAllUsersAsync()
        {
            using var db = DatabaseService.GetDatabase();
            return await db.Queryable<User>()
                          .OrderBy(u => u.Id)
                          .ToListAsync();
        }

        /// <summary>
        /// 根据ID获取用户
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<User?> GetUserByIdAsync(int id)
        {
            using var db = DatabaseService.GetDatabase();
            return await db.Queryable<User>()
                          .Where(u => u.Id == id)
                          .FirstAsync();
        }

        /// <summary>
        /// 添加用户
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        public async Task<bool> AddUserAsync(User user)
        {
            using var db = DatabaseService.GetDatabase();
            var result = await db.Insertable(user).ExecuteCommandAsync();
            return result > 0;
        }

        /// <summary>
        /// 更新用户
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        public async Task<bool> UpdateUserAsync(User user)
        {
            using var db = DatabaseService.GetDatabase();
            var result = await db.Updateable(user).ExecuteCommandAsync();
            return result > 0;
        }

        /// <summary>
        /// 删除用户
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<bool> DeleteUserAsync(int id)
        {
            using var db = DatabaseService.GetDatabase();
            var result = await db.Deleteable<User>()
                                .Where(u => u.Id == id)
                                .ExecuteCommandAsync();
            return result > 0;
        }

        /// <summary>
        /// 根据名称搜索用户
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        public async Task<List<User>> SearchUsersByNameAsync(string name)
        {
            using var db = DatabaseService.GetDatabase();
            return await db.Queryable<User>()
                          .Where(u => u.Name.Contains(name))
                          .OrderBy(u => u.Id)
                          .ToListAsync();
        }
    }
}
