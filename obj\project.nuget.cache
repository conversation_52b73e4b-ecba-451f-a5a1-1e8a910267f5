{"version": 2, "dgSpecHash": "RC0rkIT0q9s=", "success": true, "projectFilePath": "C:\\Users\\<USER>\\Desktop\\Demo\\SqlSugarDemo.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\sqlsugar\\5.1.4.198\\sqlsugar.5.1.4.198.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\stub.system.data.sqlite.core.netstandard\\1.0.118\\stub.system.data.sqlite.core.netstandard.1.0.118.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.data.sqlite.core\\1.0.118\\system.data.sqlite.core.1.0.118.nupkg.sha512"], "logs": [{"code": "NU1701", "level": "Warning", "message": "已使用“.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1”而不是项目目标框架“net8.0-windows7.0”还原包“SqlSugar 5.1.4.198”。此包可能与项目不完全兼容。", "projectPath": "C:\\Users\\<USER>\\Desktop\\Demo\\SqlSugarDemo.csproj", "warningLevel": 1, "filePath": "C:\\Users\\<USER>\\Desktop\\Demo\\SqlSugarDemo.csproj", "libraryId": "SqlSugar", "targetGraphs": ["net8.0-windows7.0"]}]}