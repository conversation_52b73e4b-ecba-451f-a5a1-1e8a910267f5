using SqlSugar;

namespace SqlSugarDemo.Models
{
    /// <summary>
    /// 用户实体类
    /// </summary>
    [SugarTable("Users")]
    public class User
    {
        /// <summary>
        /// 用户ID，主键，自增
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 邮箱
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = false)]
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// 年龄
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? Age { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime CreateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 是否激活
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsActive { get; set; } = true;
    }
}
