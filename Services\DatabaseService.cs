using SqlSugar;
using SqlSugarDemo.Models;

namespace SqlSugarDemo.Services
{
    /// <summary>
    /// 数据库服务类
    /// </summary>
    public static class DatabaseService
    {
        private static readonly string ConnectionString = "Data Source=demo.db;Version=3;";

        /// <summary>
        /// 获取SqlSugar数据库实例
        /// </summary>
        /// <returns></returns>
        public static SqlSugarScope GetDatabase()
        {
            return new SqlSugarScope(new ConnectionConfig()
            {
                ConnectionString = ConnectionString,
                DbType = DbType.Sqlite,
                IsAutoCloseConnection = true,
                InitKeyType = InitKeyType.Attribute
            },
            db =>
            {
                // 开发环境下打印SQL语句
                db.Aop.OnLogExecuting = (sql, pars) =>
                {
                    Console.WriteLine($"SQL: {sql}");
                    if (pars != null && pars.Length > 0)
                    {
                        Console.WriteLine($"Parameters: {string.Join(", ", pars.Select(p => $"{p.ParameterName}={p.Value}"))}");
                    }
                };
            });
        }

        /// <summary>
        /// 初始化数据库
        /// </summary>
        public static void InitializeDatabase()
        {
            using var db = GetDatabase();
            
            // 创建表
            db.CodeFirst.InitTables<User>();
            
            // 检查是否有初始数据，如果没有则插入示例数据
            if (!db.Queryable<User>().Any())
            {
                var users = new List<User>
                {
                    new User { Name = "张三", Email = "<EMAIL>", Age = 25 },
                    new User { Name = "李四", Email = "<EMAIL>", Age = 30 },
                    new User { Name = "王五", Email = "<EMAIL>", Age = 28 }
                };
                
                db.Insertable(users).ExecuteCommand();
            }
        }
    }
}
