using SqlSugarDemo.Models;
using SqlSugarDemo.Repositories;

namespace SqlSugarDemo.Forms
{
    public partial class MainForm : Form
    {
        private readonly IUserRepository _userRepository;
        private List<User> _users = new();

        public MainForm()
        {
            InitializeComponent();
            _userRepository = new UserRepository();
            LoadUsers();
        }

        private async void LoadUsers()
        {
            try
            {
                _users = await _userRepository.GetAllUsersAsync();
                RefreshDataGridView();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载用户数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void RefreshDataGridView()
        {
            dataGridViewUsers.DataSource = null;
            dataGridViewUsers.DataSource = _users;
            
            // 设置列标题
            if (dataGridViewUsers.Columns.Count > 0)
            {
                dataGridViewUsers.Columns["Id"].HeaderText = "ID";
                dataGridViewUsers.Columns["Name"].HeaderText = "姓名";
                dataGridViewUsers.Columns["Email"].HeaderText = "邮箱";
                dataGridViewUsers.Columns["Age"].HeaderText = "年龄";
                dataGridViewUsers.Columns["CreateTime"].HeaderText = "创建时间";
                dataGridViewUsers.Columns["IsActive"].HeaderText = "是否激活";
                
                // 设置列宽
                dataGridViewUsers.Columns["Id"].Width = 50;
                dataGridViewUsers.Columns["Name"].Width = 100;
                dataGridViewUsers.Columns["Email"].Width = 200;
                dataGridViewUsers.Columns["Age"].Width = 60;
                dataGridViewUsers.Columns["CreateTime"].Width = 150;
                dataGridViewUsers.Columns["IsActive"].Width = 80;
            }
        }

        private async void btnAdd_Click(object sender, EventArgs e)
        {
            using var form = new UserEditForm();
            if (form.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    await _userRepository.AddUserAsync(form.User);
                    LoadUsers();
                    MessageBox.Show("用户添加成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"添加用户失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private async void btnEdit_Click(object sender, EventArgs e)
        {
            if (dataGridViewUsers.CurrentRow?.DataBoundItem is User selectedUser)
            {
                using var form = new UserEditForm(selectedUser);
                if (form.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        await _userRepository.UpdateUserAsync(form.User);
                        LoadUsers();
                        MessageBox.Show("用户更新成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"更新用户失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("请选择要编辑的用户！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private async void btnDelete_Click(object sender, EventArgs e)
        {
            if (dataGridViewUsers.CurrentRow?.DataBoundItem is User selectedUser)
            {
                var result = MessageBox.Show($"确定要删除用户 '{selectedUser.Name}' 吗？", 
                    "确认删除", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                
                if (result == DialogResult.Yes)
                {
                    try
                    {
                        await _userRepository.DeleteUserAsync(selectedUser.Id);
                        LoadUsers();
                        MessageBox.Show("用户删除成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"删除用户失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("请选择要删除的用户！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private async void btnSearch_Click(object sender, EventArgs e)
        {
            var searchText = txtSearch.Text.Trim();
            
            try
            {
                if (string.IsNullOrEmpty(searchText))
                {
                    _users = await _userRepository.GetAllUsersAsync();
                }
                else
                {
                    _users = await _userRepository.SearchUsersByNameAsync(searchText);
                }
                
                RefreshDataGridView();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"搜索失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadUsers();
        }
    }
}
